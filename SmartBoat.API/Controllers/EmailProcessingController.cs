using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class EmailProcessingController : ControllerBase
    {
        private readonly ITokenStorageService _tokenStorageService;
        private readonly IEmailProcessingService _emailProcessingService;

        public EmailProcessingController(
            ITokenStorageService tokenStorageService,
            IEmailProcessingService emailProcessingService)
        {
            _tokenStorageService = tokenStorageService;
            _emailProcessingService = emailProcessingService;
        }

        [HttpPost("save-token")]
        [ProducesResponseType(200, Type = typeof(Response<string>))]
        public async Task<IActionResult> SaveToken([FromBody] Request<SaveTokenDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _tokenStorageService.SaveTokenAsync(request.Payload, request.Header.UserId.Value);
                return Ok(result);
            });
        }

        [HttpPost("exchange-token")]
        [ProducesResponseType(200, Type = typeof(Response<SaveTokenDto>))]
        public async Task<IActionResult> ExchangeToken([FromBody] Request<ExchangeTokenDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _tokenStorageService.ExchangeTokenAsync(request.Payload, request.Header.UserId.Value);
                return Ok(result);
            });
        }

        [HttpPost("token-status")]
        [ProducesResponseType(200, Type = typeof(Response<TokenStatusDto>))]
        public async Task<IActionResult> GetTokenStatus([FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _tokenStorageService.GetTokenStatusAsync(request.Header.UserId.Value);
                return Ok(result);
            });
        }

        [HttpPost("process-emails")]
        [ProducesResponseType(200, Type = typeof(Response<ProcessingStatusDto>))]
        public async Task<IActionResult> ProcessEmails([FromBody] Request<ProcessEmailsDto> request)
        {
            Console.WriteLine($"\n🎯 [API ENDPOINT] POST /api/emailprocessing/process-emails called");
            Console.WriteLine($"   👤 User ID: {request.Header.UserId.Value}");
            Console.WriteLine($"   📅 Days back: {request.Payload?.DaysBack ?? 7}");
            Console.WriteLine($"   🔄 Force reprocess: {request.Payload?.ForceReprocess == true}");

            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _emailProcessingService.ProcessEmailsAsync(request.Payload, request.Header.UserId.Value);
                Console.WriteLine($"✅ [API ENDPOINT] Process emails completed, returning result");
                return Ok(result);
            });
        }

        [HttpGet("processing-status")]
        [ProducesResponseType(200, Type = typeof(Response<ProcessingStatusDto>))]
        public async Task<IActionResult> GetProcessingStatus([FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _emailProcessingService.GetProcessingStatusAsync(request.Header.UserId.Value);
                return Ok(result);
            });
        }

        [HttpPost("summary")]
        [ProducesResponseType(200, Type = typeof(Response<ProcessingSummaryDto>))]
        public async Task<IActionResult> GetSummary([FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _emailProcessingService.GetProcessingSummaryAsync(request.Header.UserId.Value);
                return Ok(result);
            });
        }

        [HttpPost("schedule")]
        [ProducesResponseType(200, Type = typeof(Response<bool>))]
        public async Task<IActionResult> SetSchedule([FromBody] Request<bool> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _emailProcessingService.ScheduleProcessingAsync(request.Payload, request.Header.UserId.Value);
                return Ok(result);
            });
        }

        [HttpPost("schedule-status")]
        [ProducesResponseType(200, Type = typeof(Response<ScheduleStatusDto>))]
        public async Task<IActionResult> GetScheduleStatus([FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _emailProcessingService.GetScheduleStatusAsync(request.Header.UserId.Value);
                return Ok(result);
            });
        }

        [HttpPost("update-schedule")]
        [ProducesResponseType(200, Type = typeof(Response<ScheduleStatusDto>))]
        public async Task<IActionResult> UpdateSchedule([FromBody] Request<UpdateScheduleDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var result = await _emailProcessingService.UpdateScheduleAsync(request.Payload, request.Header.UserId.Value);
                return Ok(result);
            });
        }

        [HttpPost("debug-processing-log")]
        [ProducesResponseType(200, Type = typeof(Response<object>))]
        public async Task<IActionResult> DebugProcessingLog([FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                // This is a temporary debug endpoint to check EmailProcessingLog data
                var dbService = HttpContext.RequestServices.GetRequiredService<Nbg.NetCore.DatabaseService.IDatabaseService>();
                var logs = await dbService.SelectAsync<SmartBoat.API.Types.EmailProcessingLog>(new { });

                var debugInfo = new
                {
                    TotalLogs = logs?.Count() ?? 0,
                    Logs = logs?.OrderByDescending(l => l.ProcessedAt).Take(3).Select(l => (object)new
                    {
                        l.Id,
                        l.EmailSubject,
                        l.ProcessingStatus,
                        l.RecordsProcessed,
                        l.ProcessedAt
                    }).ToList() ?? new List<object>()
                };

                return Ok(new Response<object>
                {
                    Payload = debugInfo,
                    Exception = null
                });
            });
        }

        [HttpPost("debug-system-user")]
        [ProducesResponseType(200, Type = typeof(Response<object>))]
        public async Task<IActionResult> DebugSystemUser([FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                // Debug endpoint to check system user status
                var dbService = HttpContext.RequestServices.GetRequiredService<Nbg.NetCore.DatabaseService.IDatabaseService>();

                // Check Super Admin users
                var superAdminUsers = await dbService.SelectAsync<SmartBoat.API.Types.User>(new { Role = "Super Admin" });
                var activeSuperAdmins = superAdminUsers?.Where(u => u.Status == "Active" && u.IsDeleted == false).ToList();

                // Check all active users
                var allActiveUsers = await dbService.SelectAsync<SmartBoat.API.Types.User>(new { Status = "Active" });
                var activeUsersList = allActiveUsers?.Where(u => u.IsDeleted == false).ToList();

                var debugInfo = new
                {
                    SuperAdminUsers = new
                    {
                        Total = superAdminUsers?.Count() ?? 0,
                        Active = activeSuperAdmins?.Count() ?? 0,
                        Users = activeSuperAdmins?.Select(u => new
                        {
                            u.Id,
                            u.Username,
                            u.Email,
                            u.Role,
                            u.Status,
                            u.IsDeleted
                        }).ToList() ?? new List<object>()
                    },
                    AllActiveUsers = new
                    {
                        Total = activeUsersList?.Count() ?? 0,
                        Users = activeUsersList?.Take(5).Select(u => new
                        {
                            u.Id,
                            u.Username,
                            u.Email,
                            u.Role,
                            u.Status
                        }).ToList() ?? new List<object>()
                    }
                };

                return Ok(new Response<object> { Payload = debugInfo });
            });
        }

        [HttpPost("debug-schedule-table")]
        [ProducesResponseType(200, Type = typeof(Response<object>))]
        public async Task<IActionResult> DebugScheduleTable([FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var dbService = HttpContext.RequestServices.GetRequiredService<Nbg.NetCore.DatabaseService.IDatabaseService>();

                try
                {
                    // Try to query the EmailProcessingSchedule table
                    var schedules = await dbService.SelectAsync<SmartBoat.API.Types.EmailProcessingSchedule>(new { });

                    var debugInfo = new
                    {
                        TableExists = true,
                        TotalSchedules = schedules?.Count() ?? 0,
                        Schedules = schedules?.Take(3).Select(s => (object)new
                        {
                            s.Id,
                            s.UserId,
                            s.IsEnabled,
                            s.ScheduleType,
                            s.TimeZone,
                            s.CreatedAt
                        }).ToList() ?? new List<object>()
                    };

                    return Ok(new Response<object>
                    {
                        Payload = debugInfo,
                        Exception = null
                    });
                }
                catch (Exception ex)
                {
                    var debugInfo = new
                    {
                        TableExists = false,
                        ErrorMessage = ex.Message,
                        IsTableNotFoundError = ex.Message.Contains("Invalid object name 'EmailProcessingSchedule'")
                    };

                    return Ok(new Response<object>
                    {
                        Payload = debugInfo,
                        Exception = null
                    });
                }
            });
        }
    }
}